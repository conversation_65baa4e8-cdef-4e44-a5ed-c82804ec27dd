
'use client';

import { useState, useRef, useCallback } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Copy, ExternalLink, XCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Check for BarcodeDetector API support
const isBarcodeDetectorSupported = typeof window !== 'undefined' && 'BarcodeDetector' in window;

export function QrCodeReaderTool() {
  const { toast } = useToast();
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processBarcode = useCallback(async (source: HTMLImageElement) => {
    try {
      // @ts-ignore
      const barcodeDetector = new window.BarcodeDetector({ formats: ['qr_code', 'code_128', 'ean_13', 'upc_a'] });
      const barcodes = await barcodeDetector.detect(source);

      if (barcodes.length > 0) {
        const firstBarcode = barcodes[0];
        setResult(firstBarcode.rawValue);
        toast({ title: "تم العثور على الرمز!", description: "تم فك تشفير الرمز بنجاح." });
        return true;
      }
    } catch (err) {
      console.error("Barcode detection error:", err);
      setError("حدث خطأ أثناء محاولة قراءة الرمز.");
    }
    return false;
  }, [toast]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setResult(null);

    if (!isBarcodeDetectorSupported) {
      setError("متصفحك لا يدعم قارئ الباركود. يرجى استخدام متصفح حديث مثل Chrome أو Safari.");
      return;
    }
    
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = async () => {
          const found = await processBarcode(img);
          if (!found) {
            setError('لم يتم العثور على رمز QR أو باركود صالح في الصورة.');
          }
        };
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result);
      toast({ title: "تم النسخ إلى الحافظة" });
    }
  };

  const isUrl = (text: string) => {
    try {
      new URL(text);
      return true;
    } catch (_) {
      return false;
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>قارئ الباركود ورمز QR</CardTitle>
        <CardDescription>فك تشفير رموز QR والباركود عن طريق رفع صورة من جهازك.</CardDescription>
      </CardHeader>
      <CardContent>
        {!isBarcodeDetectorSupported && (
             <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>المتصفح غير مدعوم</AlertTitle>
                <AlertDescription>
                    للأسف، متصفحك الحالي لا يدعم قارئ الباركود المدمج. للحصول على أفضل تجربة، يرجى استخدام آخر إصدار من Google Chrome أو Safari.
                </AlertDescription>
            </Alert>
        )}
        
        <div className="mt-4 p-4 border-dashed border-2 rounded-lg text-center">
          <Button onClick={() => fileInputRef.current?.click()} disabled={!isBarcodeDetectorSupported}>
            <Upload className="ml-2 h-4 w-4" />
            اختر صورة من جهازك
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
          />
        </div>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <XCircle className="h-4 w-4" />
            <AlertTitle>خطأ</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">البيانات المستخرجة:</h3>
            <div className="relative">
              <Textarea
                readOnly
                value={result}
                className="min-h-[120px] bg-muted pr-12"
              />
              <div className="absolute top-2 left-2 flex flex-col gap-2">
                <Button variant="outline" size="icon" onClick={copyToClipboard} title="نسخ">
                  <Copy className="h-4 w-4" />
                </Button>
                {isUrl(result) && (
                  <Button variant="outline" size="icon" asChild title="فتح الرابط">
                    <a href={result} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
